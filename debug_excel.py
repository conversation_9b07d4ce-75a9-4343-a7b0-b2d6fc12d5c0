import openpyxl
import os

def debug_excel_content(excel_path):
    """
    调试Excel文件内容，查看AU65及周围单元格的内容
    """
    try:
        if not os.path.exists(excel_path):
            print(f"Excel文件不存在: {excel_path}")
            return
            
        workbook = openpyxl.load_workbook(excel_path)
        worksheet = workbook.active
        
        print(f"Excel文件: {excel_path}")
        print(f"工作表名称: {worksheet.title}")
        print(f"最大行数: {worksheet.max_row}")
        print(f"最大列数: {worksheet.max_column}")
        print("-" * 50)
        
        # 检查AU列的一些单元格
        target_column = 'AU'
        target_row = 65
        
        print(f"检查 {target_column} 列周围的内容:")
        
        # 检查目标行前后几行的内容
        for row in range(max(1, target_row - 3), min(worksheet.max_row + 1, target_row + 4)):
            cell_address = f"{target_column}{row}"
            cell_value = worksheet[cell_address].value
            cell_type = type(cell_value).__name__
            
            marker = " ← 目标单元格" if row == target_row else ""
            print(f"{cell_address}: {cell_value} (类型: {cell_type}){marker}")
        
        print("-" * 50)
        
        # 检查AU65单元格的详细信息
        target_cell = worksheet[f"{target_column}{target_row}"]
        print(f"目标单元格 {target_column}{target_row} 详细信息:")
        print(f"  值: {target_cell.value}")
        print(f"  类型: {type(target_cell.value)}")
        print(f"  数据类型: {target_cell.data_type}")
        print(f"  数字格式: {target_cell.number_format}")
        
        # 如果是公式，显示公式
        if hasattr(target_cell, 'formula') and target_cell.formula:
            print(f"  公式: {target_cell.formula}")
            
        # 检查是否有其他工作表
        print("-" * 50)
        print("所有工作表:")
        for sheet_name in workbook.sheetnames:
            print(f"  - {sheet_name}")
            
    except Exception as e:
        print(f"调试Excel文件出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_excel_content("底层.xlsx")
