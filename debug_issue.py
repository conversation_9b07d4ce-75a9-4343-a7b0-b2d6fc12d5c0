import openpyxl
from docx import Document
import re

def debug_the_issue():
    """专门调试K202问题"""
    
    print("=== 调试K202问题 ===")
    
    # 1. 重新读取Excel数据
    print("1. 读取Excel数据:")
    try:
        workbook = openpyxl.load_workbook("底层.xlsx")
        worksheet = workbook.active
        cell_value = worksheet['AU65'].value
        
        print(f"   AU65原始值: {repr(cell_value)}")
        print(f"   数据类型: {type(cell_value)}")
        print(f"   转为字符串: {repr(str(cell_value))}")
        print(f"   转为整数: {repr(int(cell_value))}")
        
        # 测试不同的转换方式
        test_value = int(cell_value)
        print(f"   测试值: {test_value}")
        
    except Exception as e:
        print(f"   Excel读取错误: {e}")
        return
    
    print()
    
    # 2. 测试字符串替换
    print("2. 测试字符串替换:")
    test_text = "8月24日整体人工请求量，环比下降2.5%"
    print(f"   原始文本: {test_text}")
    
    # 测试不同的替换方式
    pattern = r'(整体人工请求量)([^，]*)(，)'
    
    # 方式1：直接替换
    result1 = re.sub(pattern, f'\\1{test_value}\\3', test_text)
    print(f"   方式1结果: {result1}")
    
    # 方式2：转为字符串后替换
    result2 = re.sub(pattern, f'\\1{str(test_value)}\\3', test_text)
    print(f"   方式2结果: {result2}")
    
    # 方式3：使用format
    result3 = re.sub(pattern, lambda m: f'{m.group(1)}{test_value}{m.group(3)}', test_text)
    print(f"   方式3结果: {result3}")
    
    print()
    
    # 3. 检查Word文档当前状态
    print("3. 检查Word文档当前状态:")
    try:
        doc = Document("8月24日日报.docx")
        
        for i, paragraph in enumerate(doc.paragraphs):
            if "整体人工请求量" in paragraph.text or "K202" in paragraph.text:
                print(f"   段落{i+1}: {paragraph.text}")
                
                # 检查每个字符
                text = paragraph.text
                for j, char in enumerate(text):
                    if char in "K0123456789":
                        print(f"     位置{j}: '{char}' (Unicode: {ord(char)})")
                        
    except Exception as e:
        print(f"   Word读取错误: {e}")
    
    print()
    
    # 4. 测试简单的Word操作
    print("4. 测试简单的Word操作:")
    try:
        # 创建一个新文档测试
        from docx import Document
        test_doc = Document()
        
        # 添加测试段落
        test_paragraph = test_doc.add_paragraph("8月24日整体人工请求量，环比下降2.5%")
        print(f"   创建的段落: {test_paragraph.text}")
        
        # 尝试替换
        new_text = re.sub(r'(整体人工请求量)([^，]*)(，)', 
                         f'\\1{test_value}\\3', 
                         test_paragraph.text)
        print(f"   替换后文本: {new_text}")
        
        # 设置新文本
        test_paragraph.text = new_text
        print(f"   设置后段落: {test_paragraph.text}")
        
        # 保存测试文档
        test_doc.save("测试文档.docx")
        print("   测试文档已保存")
        
    except Exception as e:
        print(f"   Word操作错误: {e}")

if __name__ == "__main__":
    debug_the_issue()
