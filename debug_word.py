from docx import Document
import os

def debug_word_content(doc_path):
    """
    调试Word文档内容，查找包含"整体人工请求量"的段落
    """
    try:
        if not os.path.exists(doc_path):
            print(f"Word文件不存在: {doc_path}")
            return
            
        doc = Document(doc_path)
        
        print(f"Word文件: {doc_path}")
        print("=" * 60)
        
        paragraph_count = 0
        match_count = 0
        
        # 遍历所有段落
        print("检查所有段落:")
        for i, paragraph in enumerate(doc.paragraphs):
            paragraph_count += 1
            text = paragraph.text.strip()
            
            if text:  # 只显示非空段落
                print(f"段落 {i+1}: {text}")
                
                # 检查是否包含关键词
                if "整体人工请求量" in text:
                    match_count += 1
                    print(f"  ★ 找到匹配段落！")
                    print(f"  ★ 完整内容: {text}")
                    
                    # 分析runs
                    print(f"  ★ 该段落的runs数量: {len(paragraph.runs)}")
                    for j, run in enumerate(paragraph.runs):
                        print(f"    Run {j+1}: '{run.text}'")
        
        print("-" * 60)
        
        # 遍历表格
        print("检查所有表格:")
        table_count = 0
        for table_idx, table in enumerate(doc.tables):
            table_count += 1
            print(f"表格 {table_idx + 1}:")
            
            for row_idx, row in enumerate(table.rows):
                for cell_idx, cell in enumerate(row.cells):
                    cell_text = cell.text.strip()
                    if cell_text and "整体人工请求量" in cell_text:
                        match_count += 1
                        print(f"  ★ 在表格 {table_idx+1} 行 {row_idx+1} 列 {cell_idx+1} 找到匹配:")
                        print(f"  ★ 内容: {cell_text}")
        
        print("=" * 60)
        print(f"总计:")
        print(f"  段落数量: {paragraph_count}")
        print(f"  表格数量: {table_count}")
        print(f"  包含'整体人工请求量'的位置: {match_count}")
        
        # 如果没有找到匹配，搜索相似的内容
        if match_count == 0:
            print("\n没有找到'整体人工请求量'，搜索相似内容:")
            keywords = ["人工请求", "请求量", "K202", "35510", "13202"]
            
            for keyword in keywords:
                print(f"\n搜索关键词: '{keyword}'")
                found = False
                
                for i, paragraph in enumerate(doc.paragraphs):
                    if keyword in paragraph.text:
                        found = True
                        print(f"  段落 {i+1}: {paragraph.text}")
                
                if not found:
                    print(f"  未找到包含'{keyword}'的内容")
                    
    except Exception as e:
        print(f"调试Word文件出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_word_content("8月24日日报.docx")
