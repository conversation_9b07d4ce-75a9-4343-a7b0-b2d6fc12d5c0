import openpyxl
from docx import Document
import re
import os

class AdvancedExcelToWordFiller:
    def __init__(self):
        pass
    
    def read_excel_cell(self, excel_path, column, row):
        """读取Excel指定单元格的数据"""
        try:
            workbook = openpyxl.load_workbook(excel_path)
            worksheet = workbook.active
            
            cell_address = f"{column}{row}"
            cell_value = worksheet[cell_address].value
            
            print(f"读取到Excel {cell_address}单元格的值: {cell_value}")
            
            if cell_value is not None:
                if isinstance(cell_value, (int, float)):
                    return int(cell_value) if cell_value == int(cell_value) else cell_value
                else:
                    return str(cell_value)
            return cell_value
            
        except Exception as e:
            print(f"读取Excel文件出错: {e}")
            return None
    
    def fill_word_document(self, doc_path, target_value, output_path):
        """高级填充Word文档，处理复杂的runs结构"""
        try:
            doc = Document(doc_path)
            fill_count = 0
            
            print(f"开始处理Word文档，要填入的值: {target_value}")
            print("-" * 50)
            
            # 遍历所有段落
            for i, paragraph in enumerate(doc.paragraphs):
                if "整体人工请求量" in paragraph.text:
                    print(f"段落 {i+1}: {paragraph.text}")
                    print(f"  Runs详情:")
                    for j, run in enumerate(paragraph.runs):
                        print(f"    Run {j+1}: '{run.text}'")
                    
                    # 检查是否需要处理
                    if self._should_fill_paragraph(paragraph.text):
                        print(f"  → 需要填充数值")
                        success = self._fill_paragraph_advanced(paragraph, target_value)
                        if success:
                            fill_count += 1
                            print(f"  → 填充成功")
                            print(f"  → 新内容: {paragraph.text}")
                        else:
                            print(f"  → 填充失败")
                    else:
                        print(f"  → 不需要填充")
                    print()
            
            # 保存文档
            doc.save(output_path)
            print(f"文档已保存到: {output_path}")
            print(f"总共填充了 {fill_count} 处位置")
            
            return fill_count > 0
            
        except Exception as e:
            print(f"处理Word文档出错: {e}")
            return False
    
    def _should_fill_paragraph(self, text):
        """判断段落是否需要填充数值"""
        pattern = r'整体人工请求量[^，]*，'
        return bool(re.search(pattern, text))
    
    def _fill_paragraph_advanced(self, paragraph, target_value):
        """高级段落填充，重建整个段落"""
        try:
            # 获取原始文本
            original_text = paragraph.text
            
            # 显示匹配信息
            match = re.search(r'(整体人工请求量)([^，]*)(，)', original_text)
            if match:
                print(f"    匹配内容: '{match.group(0)}'")
                print(f"    中间部分: '{match.group(2)}'")
            
            # 构建新文本
            new_text = re.sub(r'(整体人工请求量)([^，]*)(，)', 
                             f'\\1{target_value}\\3', 
                             original_text)
            
            print(f"    目标文本: {new_text}")
            
            # 完全重建段落内容
            self._rebuild_paragraph(paragraph, new_text)
            
            return True
            
        except Exception as e:
            print(f"填充段落出错: {e}")
            return False
    
    def _rebuild_paragraph(self, paragraph, new_text):
        """完全重建段落，避免runs问题"""
        # 保存段落的样式信息
        paragraph_format = paragraph.paragraph_format
        
        # 获取第一个run的样式（如果存在）
        first_run_font = None
        if paragraph.runs:
            first_run_font = paragraph.runs[0].font
        
        # 清空所有runs
        for run in paragraph.runs[:]:
            run._element.getparent().remove(run._element)
        
        # 创建新的run
        new_run = paragraph.add_run(new_text)
        
        # 恢复字体样式
        if first_run_font:
            new_run.font.name = first_run_font.name
            new_run.font.size = first_run_font.size
            new_run.font.bold = first_run_font.bold
            new_run.font.italic = first_run_font.italic
            new_run.font.underline = first_run_font.underline
            if first_run_font.color.rgb:
                new_run.font.color.rgb = first_run_font.color.rgb
    
    def process(self, excel_path, doc_path, output_path, column='AU', row=65):
        """主处理函数"""
        print("开始高级处理...")
        print(f"Excel文件: {excel_path}")
        print(f"Word文件: {doc_path}")
        print(f"目标单元格: {column}{row}")
        print("=" * 60)
        
        # 检查文件
        if not os.path.exists(excel_path):
            print(f"Excel文件不存在: {excel_path}")
            return False
            
        if not os.path.exists(doc_path):
            print(f"Word文件不存在: {doc_path}")
            return False
        
        # 读取Excel数据
        cell_value = self.read_excel_cell(excel_path, column, row)
        if cell_value is None:
            print("无法读取Excel数据")
            return False
        
        # 填充Word文档
        success = self.fill_word_document(doc_path, cell_value, output_path)
        
        if success:
            print("=" * 60)
            print("处理完成！")
        else:
            print("=" * 60)
            print("处理失败！")
        
        return success

if __name__ == "__main__":
    filler = AdvancedExcelToWordFiller()
    
    # 设置文件路径
    excel_file = "底层.xlsx"
    word_file = "8月24日日报.docx"
    output_file = "高级填充后的日报.docx"
    
    # 执行处理
    filler.process(excel_file, word_file, output_file, column='AU', row=65)
