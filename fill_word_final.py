import openpyxl
from docx import Document
import re
import os

class FinalExcelToWordFiller:
    def __init__(self):
        pass
    
    def read_excel_cell(self, excel_path, column, row):
        """读取Excel指定单元格的数据"""
        try:
            workbook = openpyxl.load_workbook(excel_path)
            worksheet = workbook.active
            
            cell_address = f"{column}{row}"
            cell_value = worksheet[cell_address].value
            
            print(f"读取Excel {cell_address}: {cell_value}")
            return int(cell_value) if isinstance(cell_value, (int, float)) else str(cell_value)
            
        except Exception as e:
            print(f"读取Excel出错: {e}")
            return None
    
    def fill_word_document(self, doc_path, target_value, output_path):
        """填充Word文档"""
        try:
            doc = Document(doc_path)
            fill_count = 0
            
            print(f"开始填充，目标值: {target_value}")
            print("-" * 40)
            
            # 遍历所有段落
            for i, paragraph in enumerate(doc.paragraphs):
                if "整体人工请求量" in paragraph.text:
                    original_text = paragraph.text
                    print(f"段落{i+1}: {original_text}")
                    
                    # 检查是否需要处理
                    if re.search(r'整体人工请求量[^，]*，', original_text):
                        # 使用lambda函数确保正确替换
                        new_text = re.sub(
                            r'(整体人工请求量)([^，]*)(，)',
                            lambda match: f'{match.group(1)}{target_value}{match.group(3)}',
                            original_text
                        )
                        
                        print(f"  → 替换为: {new_text}")
                        
                        # 重建段落
                        self._rebuild_paragraph_simple(paragraph, new_text)
                        fill_count += 1
                        
                        # 验证结果
                        print(f"  → 实际结果: {paragraph.text}")
                    else:
                        print(f"  → 跳过（不符合模式）")
                    print()
            
            # 保存文档
            doc.save(output_path)
            print(f"已保存到: {output_path}")
            print(f"填充了 {fill_count} 处")
            
            return fill_count > 0
            
        except Exception as e:
            print(f"处理Word文档出错: {e}")
            return False
    
    def _rebuild_paragraph_simple(self, paragraph, new_text):
        """简单重建段落"""
        # 清空段落
        paragraph.clear()
        # 添加新文本
        paragraph.add_run(new_text)
    
    def process(self, excel_path, doc_path, output_path, column='AU', row=65):
        """主处理函数"""
        print("=== 最终版本处理 ===")
        
        # 读取Excel数据
        cell_value = self.read_excel_cell(excel_path, column, row)
        if cell_value is None:
            print("无法读取Excel数据")
            return False
        
        # 填充Word文档
        success = self.fill_word_document(doc_path, cell_value, output_path)
        
        print("=== 处理完成 ===")
        return success

if __name__ == "__main__":
    filler = FinalExcelToWordFiller()
    
    # 执行处理
    filler.process(
        excel_path="底层.xlsx",
        doc_path="8月24日日报.docx", 
        output_path="最终填充后的日报.docx",
        column='AU', 
        row=65
    )
