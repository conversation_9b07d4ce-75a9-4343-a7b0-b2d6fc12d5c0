import openpyxl
from docx import Document
import re
import os

class ExcelToWordFiller:
    def __init__(self):
        pass
    
    def read_excel_cell(self, excel_path, column, row):
        """
        读取Excel指定单元格的数据
        column: 列名，如'AU'
        row: 行号，如65
        """
        try:
            workbook = openpyxl.load_workbook(excel_path)
            worksheet = workbook.active

            # 构造单元格地址，如'AU65'
            cell_address = f"{column}{row}"
            cell_value = worksheet[cell_address].value

            print(f"读取到Excel {cell_address}单元格的值: {cell_value}")
            print(f"数据类型: {type(cell_value)}")

            # 确保返回的是数字或字符串
            if cell_value is not None:
                # 如果是数字，转换为整数或保持原样
                if isinstance(cell_value, (int, float)):
                    if cell_value == int(cell_value):
                        return int(cell_value)
                    else:
                        return cell_value
                else:
                    return str(cell_value)

            return cell_value

        except Exception as e:
            print(f"读取Excel文件出错: {e}")
            return None
    
    def fill_word_document(self, doc_path, target_value, output_path):
        """
        在Word文档中找到"整体人工请求量"和"，"之间的位置，填入数值
        保持原有格式
        """
        try:
            doc = Document(doc_path)
            fill_count = 0  # 记录填充次数

            # 遍历所有段落
            for paragraph in doc.paragraphs:
                fill_count += self._fill_paragraph_runs(paragraph, target_value)

            # 遍历表格中的内容
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        for paragraph in cell.paragraphs:
                            fill_count += self._fill_paragraph_runs(paragraph, target_value)

            # 保存文档
            doc.save(output_path)
            print(f"文档已保存到: {output_path}")
            print(f"总共填充了 {fill_count} 处位置")

            return fill_count > 0

        except Exception as e:
            print(f"处理Word文档出错: {e}")
            return False

    def _fill_paragraph_runs(self, paragraph, target_value):
        """
        在段落的runs中查找并替换，保持格式
        """
        if not paragraph:
            return 0

        original_text = paragraph.text

        # 如果段落包含"整体人工请求量"，打印调试信息
        if "整体人工请求量" in original_text:
            print(f"发现包含'整体人工请求量'的段落: {original_text}")

        # 查找"整体人工请求量"和"，"之间的内容
        pattern = r'(整体人工请求量)([^，]*)(，)'
        matches = re.findall(pattern, original_text)

        if not matches:
            return 0

        print(f"找到匹配的内容: {original_text}")
        print(f"匹配的部分: {matches}")
        print(f"要替换的值: {target_value} (类型: {type(target_value)})")

        # 构建新文本
        new_text = re.sub(pattern, f'\\1{target_value}\\3', original_text)
        print(f"替换后的内容: {new_text}")

        # 保持格式的替换方法
        self._replace_text_keep_format(paragraph, original_text, new_text)

        return 1

    def _replace_text_keep_format(self, paragraph, old_text, new_text):
        """
        替换文本但保持格式
        """
        # 清空段落内容但保持runs的格式
        if paragraph.runs:
            # 保存第一个run的格式
            first_run = paragraph.runs[0]

            # 清空所有runs
            for run in paragraph.runs:
                run.text = ""

            # 在第一个run中设置新文本
            first_run.text = new_text
        else:
            # 如果没有runs，直接设置文本
            paragraph.text = new_text
    
    def process(self, excel_path, doc_path, output_path, column='AU', row=65):
        """
        主处理函数
        """
        print("开始处理...")
        print(f"Excel文件: {excel_path}")
        print(f"Word文件: {doc_path}")
        print(f"目标单元格: {column}{row}")
        
        # 检查文件是否存在
        if not os.path.exists(excel_path):
            print(f"Excel文件不存在: {excel_path}")
            return False
            
        if not os.path.exists(doc_path):
            print(f"Word文件不存在: {doc_path}")
            return False
        
        # 读取Excel数据
        cell_value = self.read_excel_cell(excel_path, column, row)
        if cell_value is None:
            print("无法读取Excel数据")
            return False
        
        # 填充Word文档
        success = self.fill_word_document(doc_path, cell_value, output_path)
        
        if success:
            print("处理完成！")
        else:
            print("处理失败！")
        
        return success

# 使用示例
if __name__ == "__main__":
    filler = ExcelToWordFiller()
    
    # 设置文件路径
    excel_file = "底层.xlsx"
    word_file = "8月24日日报.docx"
    output_file = "填充后的日报.docx"
    
    # 执行处理
    filler.process(excel_file, word_file, output_file, column='AU', row=65)
