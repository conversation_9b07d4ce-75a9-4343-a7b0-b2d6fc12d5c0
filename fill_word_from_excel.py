import openpyxl
from docx import Document
import re
import os

class ExcelToWordFiller:
    def __init__(self):
        pass
    
    def read_excel_cell(self, excel_path, column, row):
        """
        读取Excel指定单元格的数据
        column: 列名，如'AU'
        row: 行号，如65
        """
        try:
            workbook = openpyxl.load_workbook(excel_path)
            worksheet = workbook.active
            
            # 构造单元格地址，如'AU65'
            cell_address = f"{column}{row}"
            cell_value = worksheet[cell_address].value
            
            print(f"读取到Excel {cell_address}单元格的值: {cell_value}")
            return cell_value
            
        except Exception as e:
            print(f"读取Excel文件出错: {e}")
            return None
    
    def fill_word_document(self, doc_path, target_value, output_path):
        """
        在Word文档中找到"整体人工请求量"和"，"之间的位置，填入数值
        """
        try:
            doc = Document(doc_path)
            fill_count = 0  # 记录填充次数
            
            # 遍历所有段落
            for paragraph in doc.paragraphs:
                original_text = paragraph.text
                
                # 查找"整体人工请求量"和"，"之间的内容
                pattern = r'(整体人工请求量)([^，]*)(，)'
                matches = re.findall(pattern, original_text)
                
                if matches:
                    print(f"找到匹配的段落: {original_text}")
                    
                    # 替换匹配的内容
                    new_text = re.sub(pattern, f'\\1{target_value}\\3', original_text)
                    paragraph.text = new_text
                    fill_count += 1
                    
                    print(f"替换后的段落: {new_text}")
            
            # 遍历表格中的内容
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        for paragraph in cell.paragraphs:
                            original_text = paragraph.text
                            
                            # 查找"整体人工请求量"和"，"之间的内容
                            pattern = r'(整体人工请求量)([^，]*)(，)'
                            matches = re.findall(pattern, original_text)
                            
                            if matches:
                                print(f"在表格中找到匹配的内容: {original_text}")
                                
                                # 替换匹配的内容
                                new_text = re.sub(pattern, f'\\1{target_value}\\3', original_text)
                                paragraph.text = new_text
                                fill_count += 1
                                
                                print(f"表格中替换后的内容: {new_text}")
            
            # 保存文档
            doc.save(output_path)
            print(f"文档已保存到: {output_path}")
            print(f"总共填充了 {fill_count} 处位置")
            
            return fill_count > 0
            
        except Exception as e:
            print(f"处理Word文档出错: {e}")
            return False
    
    def process(self, excel_path, doc_path, output_path, column='AU', row=65):
        """
        主处理函数
        """
        print("开始处理...")
        print(f"Excel文件: {excel_path}")
        print(f"Word文件: {doc_path}")
        print(f"目标单元格: {column}{row}")
        
        # 检查文件是否存在
        if not os.path.exists(excel_path):
            print(f"Excel文件不存在: {excel_path}")
            return False
            
        if not os.path.exists(doc_path):
            print(f"Word文件不存在: {doc_path}")
            return False
        
        # 读取Excel数据
        cell_value = self.read_excel_cell(excel_path, column, row)
        if cell_value is None:
            print("无法读取Excel数据")
            return False
        
        # 填充Word文档
        success = self.fill_word_document(doc_path, cell_value, output_path)
        
        if success:
            print("处理完成！")
        else:
            print("处理失败！")
        
        return success

# 使用示例
if __name__ == "__main__":
    filler = ExcelToWordFiller()
    
    # 设置文件路径
    excel_file = "底层.xlsx"
    word_file = "8月24日日报.docx"
    output_file = "填充后的日报.docx"
    
    # 执行处理
    filler.process(excel_file, word_file, output_file, column='AU', row=65)
