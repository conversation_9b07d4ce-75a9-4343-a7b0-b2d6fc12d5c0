import openpyxl
from docx import Document
import re
import os

class FormatKeepingFiller:
    def __init__(self):
        pass
    
    def read_excel_cell(self, excel_path, column, row):
        """读取Excel指定单元格的数据"""
        try:
            workbook = openpyxl.load_workbook(excel_path)
            worksheet = workbook.active
            
            cell_address = f"{column}{row}"
            cell_value = worksheet[cell_address].value
            
            print(f"读取Excel {cell_address}: {cell_value}")
            return int(cell_value) if isinstance(cell_value, (int, float)) else str(cell_value)
            
        except Exception as e:
            print(f"读取Excel出错: {e}")
            return None
    
    def fill_word_document(self, doc_path, target_value, output_path):
        """填充Word文档，保持格式"""
        try:
            doc = Document(doc_path)
            fill_count = 0
            
            print(f"开始填充，目标值: {target_value}")
            print("-" * 40)
            
            # 遍历所有段落
            for i, paragraph in enumerate(doc.paragraphs):
                if "整体人工请求量" in paragraph.text:
                    original_text = paragraph.text
                    print(f"段落{i+1}: {original_text}")
                    
                    # 检查是否需要处理
                    if re.search(r'整体人工请求量[^，]*，', original_text):
                        # 计算新文本
                        new_text = re.sub(
                            r'(整体人工请求量)([^，]*)(，)',
                            lambda match: f'{match.group(1)}{target_value}{match.group(3)}',
                            original_text
                        )
                        
                        print(f"  → 目标文本: {new_text}")
                        
                        # 在runs中精确替换，保持格式
                        success = self._replace_in_runs(paragraph, original_text, new_text)
                        
                        if success:
                            fill_count += 1
                            print(f"  → 替换成功: {paragraph.text}")
                        else:
                            print(f"  → 替换失败")
                    else:
                        print(f"  → 跳过（不符合模式）")
                    print()
            
            # 保存文档
            doc.save(output_path)
            print(f"已保存到: {output_path}")
            print(f"填充了 {fill_count} 处")
            
            return fill_count > 0
            
        except Exception as e:
            print(f"处理Word文档出错: {e}")
            return False
    
    def _replace_in_runs(self, paragraph, old_text, new_text):
        """在runs中替换文本，保持格式"""
        try:
            # 找到需要替换的部分
            match = re.search(r'(整体人工请求量)([^，]*)(，)', old_text)
            if not match:
                return False
            
            # 要替换的原始部分和新部分
            old_part = match.group(0)  # 例如："整体人工请求量，"
            new_part = f"{match.group(1)}{self.target_value}{match.group(3)}"  # 例如："整体人工请求量13202，"
            
            # 在runs中查找并替换
            full_text = ""
            run_positions = []
            
            # 记录每个run的文本和位置
            for run in paragraph.runs:
                start_pos = len(full_text)
                full_text += run.text
                end_pos = len(full_text)
                run_positions.append((run, start_pos, end_pos, run.text))
            
            # 找到要替换部分在完整文本中的位置
            old_start = old_text.find(old_part)
            old_end = old_start + len(old_part)
            
            # 找到对应的runs
            affected_runs = []
            for run, start_pos, end_pos, run_text in run_positions:
                if start_pos < old_end and end_pos > old_start:
                    affected_runs.append((run, start_pos, end_pos, run_text))
            
            if not affected_runs:
                return False
            
            # 简单方案：如果替换部分跨越多个runs，就用第一个run承载所有新文本
            first_run = affected_runs[0][0]
            
            # 计算新文本
            before_part = old_text[:old_start]
            after_part = old_text[old_end:]
            complete_new_text = before_part + new_part + after_part
            
            # 清空所有runs的文本
            for run in paragraph.runs:
                run.text = ""
            
            # 在第一个run中设置完整的新文本
            first_run.text = complete_new_text
            
            return True
            
        except Exception as e:
            print(f"  替换runs出错: {e}")
            return False
    
    def process(self, excel_path, doc_path, output_path, column='AU', row=65):
        """主处理函数"""
        print("=== 保持格式版本处理 ===")
        
        # 读取Excel数据
        cell_value = self.read_excel_cell(excel_path, column, row)
        if cell_value is None:
            print("无法读取Excel数据")
            return False
        
        # 保存目标值供其他方法使用
        self.target_value = cell_value
        
        # 填充Word文档
        success = self.fill_word_document(doc_path, cell_value, output_path)
        
        print("=== 处理完成 ===")
        return success

if __name__ == "__main__":
    filler = FormatKeepingFiller()
    
    # 执行处理
    filler.process(
        excel_path="底层.xlsx",
        doc_path="8月24日日报.docx", 
        output_path="保持格式的日报.docx",
        column='AU', 
        row=65
    )
