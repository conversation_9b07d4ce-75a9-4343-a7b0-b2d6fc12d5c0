import openpyxl
from docx import Document
import re
import os

class PreciseExcelToWordFiller:
    def __init__(self):
        pass
    
    def read_excel_cell(self, excel_path, column, row):
        """读取Excel指定单元格的数据"""
        try:
            workbook = openpyxl.load_workbook(excel_path)
            worksheet = workbook.active
            
            cell_address = f"{column}{row}"
            cell_value = worksheet[cell_address].value
            
            print(f"读取到Excel {cell_address}单元格的值: {cell_value}")
            
            if cell_value is not None:
                if isinstance(cell_value, (int, float)):
                    return int(cell_value) if cell_value == int(cell_value) else cell_value
                else:
                    return str(cell_value)
            return cell_value
            
        except Exception as e:
            print(f"读取Excel文件出错: {e}")
            return None
    
    def fill_word_document(self, doc_path, target_value, output_path):
        """精确填充Word文档"""
        try:
            doc = Document(doc_path)
            fill_count = 0
            
            print(f"开始处理Word文档，要填入的值: {target_value}")
            print("-" * 50)
            
            # 遍历所有段落
            for i, paragraph in enumerate(doc.paragraphs):
                if "整体人工请求量" in paragraph.text:
                    print(f"段落 {i+1}: {paragraph.text}")
                    
                    # 检查是否是我们要处理的模式
                    if self._should_fill_paragraph(paragraph.text):
                        print(f"  → 需要填充数值")
                        success = self._fill_paragraph_with_format(paragraph, target_value)
                        if success:
                            fill_count += 1
                            print(f"  → 填充成功: {paragraph.text}")
                        else:
                            print(f"  → 填充失败")
                    else:
                        print(f"  → 不需要填充（可能已有数值或不符合模式）")
                    print()
            
            # 保存文档
            doc.save(output_path)
            print(f"文档已保存到: {output_path}")
            print(f"总共填充了 {fill_count} 处位置")
            
            return fill_count > 0
            
        except Exception as e:
            print(f"处理Word文档出错: {e}")
            return False
    
    def _should_fill_paragraph(self, text):
        """判断段落是否需要填充数值"""
        # 检查是否是"整体人工请求量"后直接跟"，"的模式
        pattern = r'整体人工请求量\s*，'
        return bool(re.search(pattern, text))
    
    def _fill_paragraph_with_format(self, paragraph, target_value):
        """在段落中填充数值，保持格式"""
        try:
            # 获取原始文本
            original_text = paragraph.text
            
            # 构建新文本：在"整体人工请求量"后插入数值
            new_text = re.sub(r'(整体人工请求量)(\s*)(，)', 
                             f'\\1{target_value}\\3', 
                             original_text)
            
            # 使用更安全的方式替换文本，保持格式
            self._replace_paragraph_text(paragraph, new_text)
            
            return True
            
        except Exception as e:
            print(f"填充段落出错: {e}")
            return False
    
    def _replace_paragraph_text(self, paragraph, new_text):
        """替换段落文本但尽量保持格式"""
        # 清空现有内容
        for run in paragraph.runs:
            run.text = ""
        
        # 如果有runs，使用第一个run
        if paragraph.runs:
            paragraph.runs[0].text = new_text
        else:
            # 如果没有runs，创建一个新的
            paragraph.text = new_text
    
    def process(self, excel_path, doc_path, output_path, column='AU', row=65):
        """主处理函数"""
        print("开始精确处理...")
        print(f"Excel文件: {excel_path}")
        print(f"Word文件: {doc_path}")
        print(f"目标单元格: {column}{row}")
        print("=" * 60)
        
        # 检查文件
        if not os.path.exists(excel_path):
            print(f"Excel文件不存在: {excel_path}")
            return False
            
        if not os.path.exists(doc_path):
            print(f"Word文件不存在: {doc_path}")
            return False
        
        # 读取Excel数据
        cell_value = self.read_excel_cell(excel_path, column, row)
        if cell_value is None:
            print("无法读取Excel数据")
            return False
        
        # 填充Word文档
        success = self.fill_word_document(doc_path, cell_value, output_path)
        
        if success:
            print("=" * 60)
            print("处理完成！")
        else:
            print("=" * 60)
            print("处理失败！")
        
        return success

if __name__ == "__main__":
    filler = PreciseExcelToWordFiller()
    
    # 设置文件路径
    excel_file = "底层.xlsx"
    word_file = "8月24日日报.docx"
    output_file = "精确填充后的日报.docx"
    
    # 执行处理
    filler.process(excel_file, word_file, output_file, column='AU', row=65)
