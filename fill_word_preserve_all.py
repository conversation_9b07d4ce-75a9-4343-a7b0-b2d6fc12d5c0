import openpyxl
from docx import Document
import re
import os

class CompleteFormatPreservingFiller:
    def __init__(self):
        pass
    
    def read_excel_cell(self, excel_path, column, row):
        """读取Excel指定单元格的数据"""
        try:
            workbook = openpyxl.load_workbook(excel_path)
            worksheet = workbook.active
            
            cell_address = f"{column}{row}"
            cell_value = worksheet[cell_address].value
            
            print(f"读取Excel {cell_address}: {cell_value}")
            return int(cell_value) if isinstance(cell_value, (int, float)) else str(cell_value)
            
        except Exception as e:
            print(f"读取Excel出错: {e}")
            return None
    
    def fill_word_document(self, doc_path, target_value, output_path):
        """填充Word文档，完全保持格式"""
        try:
            doc = Document(doc_path)
            fill_count = 0
            
            print(f"开始填充，目标值: {target_value}")
            print("-" * 50)
            
            # 遍历所有段落
            for i, paragraph in enumerate(doc.paragraphs):
                if "整体人工请求量" in paragraph.text:
                    print(f"段落{i+1}: {paragraph.text}")
                    print(f"  Runs数量: {len(paragraph.runs)}")
                    
                    # 显示每个run的详细信息
                    for j, run in enumerate(paragraph.runs):
                        print(f"    Run{j+1}: '{run.text}' (上标:{run.font.superscript}, 下标:{run.font.subscript})")
                    
                    # 检查是否需要处理
                    if re.search(r'整体人工请求量[^，]*，', paragraph.text):
                        print(f"  → 需要处理")
                        
                        # 使用精确的runs替换方法
                        success = self._precise_replace_in_runs(paragraph, target_value)
                        
                        if success:
                            fill_count += 1
                            print(f"  → 替换成功")
                            print(f"  → 新文本: {paragraph.text}")
                            
                            # 显示替换后的runs信息
                            print(f"  → 新Runs数量: {len(paragraph.runs)}")
                            for j, run in enumerate(paragraph.runs):
                                print(f"      Run{j+1}: '{run.text}' (上标:{run.font.superscript}, 下标:{run.font.subscript})")
                        else:
                            print(f"  → 替换失败")
                    else:
                        print(f"  → 跳过（不符合模式）")
                    print()
            
            # 保存文档
            doc.save(output_path)
            print(f"已保存到: {output_path}")
            print(f"填充了 {fill_count} 处")
            
            return fill_count > 0
            
        except Exception as e:
            print(f"处理Word文档出错: {e}")
            return False
    
    def _precise_replace_in_runs(self, paragraph, target_value):
        """在runs中精确替换，保持所有格式"""
        try:
            # 构建完整文本和run映射
            full_text = ""
            run_map = []  # [(run_index, start_pos, end_pos, run_object)]
            
            for i, run in enumerate(paragraph.runs):
                start_pos = len(full_text)
                full_text += run.text
                end_pos = len(full_text)
                run_map.append((i, start_pos, end_pos, run))
            
            print(f"    完整文本: {full_text}")
            
            # 找到要替换的模式
            match = re.search(r'(整体人工请求量)([^，]*)(，)', full_text)
            if not match:
                return False
            
            # 计算替换位置
            replace_start = match.start(2)  # 中间部分的开始位置
            replace_end = match.end(2)      # 中间部分的结束位置
            
            print(f"    要替换的位置: {replace_start}-{replace_end}")
            print(f"    要替换的内容: '{match.group(2)}'")
            print(f"    替换为: '{target_value}'")
            
            # 找到涉及的runs
            affected_runs = []
            for run_index, start_pos, end_pos, run_obj in run_map:
                if start_pos < replace_end and end_pos > replace_start:
                    affected_runs.append((run_index, start_pos, end_pos, run_obj))
            
            print(f"    涉及的runs: {len(affected_runs)}")
            
            if not affected_runs:
                return False
            
            # 如果替换区域完全在一个run内
            if len(affected_runs) == 1:
                run_index, run_start, run_end, run_obj = affected_runs[0]
                
                # 计算在这个run内的相对位置
                relative_start = replace_start - run_start
                relative_end = replace_end - run_start
                
                # 替换run内的文本
                old_run_text = run_obj.text
                new_run_text = (old_run_text[:relative_start] + 
                               str(target_value) + 
                               old_run_text[relative_end:])
                
                run_obj.text = new_run_text
                print(f"    单run替换: '{old_run_text}' → '{new_run_text}'")
                
            else:
                # 跨多个runs的复杂情况
                print(f"    跨多个runs的替换，保持原有结构")
                
                # 找到第一个包含替换内容的run
                first_affected = affected_runs[0]
                run_index, run_start, run_end, run_obj = first_affected
                
                # 在第一个run中进行替换
                relative_start = max(0, replace_start - run_start)
                relative_end = min(len(run_obj.text), replace_end - run_start)
                
                old_run_text = run_obj.text
                
                if replace_start >= run_start and replace_end <= run_end:
                    # 替换内容完全在这个run内
                    new_run_text = (old_run_text[:relative_start] + 
                                   str(target_value) + 
                                   old_run_text[relative_end:])
                    run_obj.text = new_run_text
                else:
                    # 替换内容跨越多个runs，在第一个run中放置新内容
                    if replace_start >= run_start:
                        new_run_text = old_run_text[:relative_start] + str(target_value)
                    else:
                        new_run_text = str(target_value) + old_run_text[relative_end:]
                    
                    run_obj.text = new_run_text
                    
                    # 清空其他受影响runs中的相关内容
                    for i in range(1, len(affected_runs)):
                        other_run_index, other_start, other_end, other_run = affected_runs[i]
                        
                        # 计算这个run中需要清空的部分
                        clear_start = max(0, replace_start - other_start)
                        clear_end = min(len(other_run.text), replace_end - other_start)
                        
                        if clear_start < clear_end:
                            other_old_text = other_run.text
                            other_new_text = other_old_text[:clear_start] + other_old_text[clear_end:]
                            other_run.text = other_new_text
            
            return True
            
        except Exception as e:
            print(f"    精确替换出错: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def process(self, excel_path, doc_path, output_path, column='AU', row=65):
        """主处理函数"""
        print("=== 完全保持格式版本处理 ===")
        
        # 读取Excel数据
        cell_value = self.read_excel_cell(excel_path, column, row)
        if cell_value is None:
            print("无法读取Excel数据")
            return False
        
        # 填充Word文档
        success = self.fill_word_document(doc_path, cell_value, output_path)
        
        print("=== 处理完成 ===")
        return success

if __name__ == "__main__":
    filler = CompleteFormatPreservingFiller()
    
    # 执行处理
    filler.process(
        excel_path="底层.xlsx",
        doc_path="8月24日日报.docx", 
        output_path="完全保持格式的日报.docx",
        column='AU', 
        row=65
    )
