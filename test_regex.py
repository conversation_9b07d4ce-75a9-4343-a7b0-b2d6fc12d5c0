import re

def test_regex_patterns():
    """测试正则表达式匹配效果"""
    
    # 测试用例
    test_cases = [
        # 情况1：中间是空的
        "8月24日整体人工请求量，环比下降2.5%，较发布前日均上涨23.5%",
        
        # 情况2：中间有其他文字
        "8月24日整体人工请求量35510，环比下降2.5%，较发布前日均上涨23.5%",
        "8月24日整体人工请求量K202，环比下降2.5%，较发布前日均上涨23.5%",
        "8月24日整体人工请求量xxxxx，环比下降2.5%，较发布前日均上涨23.5%",
        
        # 情况3：图表标题（不应该被替换）
        "图1:整体人工请求量情况",
        "图2:整体人工请求量情况",
        
        # 情况4：其他包含"整体人工请求量"但没有逗号的
        "整体人工请求量分析报告",
    ]
    
    # 要替换的目标值
    target_value = 13202
    
    # 正则表达式模式
    pattern = r'(整体人工请求量)([^，]*)(，)'
    
    print("正则表达式测试结果:")
    print("=" * 80)
    
    for i, text in enumerate(test_cases, 1):
        print(f"测试用例 {i}: {text}")
        
        # 检查是否匹配
        match = re.search(pattern, text)
        if match:
            print(f"  ✓ 匹配成功")
            print(f"    完整匹配: '{match.group(0)}'")
            print(f"    第1组 (前缀): '{match.group(1)}'")
            print(f"    第2组 (中间): '{match.group(2)}'")
            print(f"    第3组 (后缀): '{match.group(3)}'")
            
            # 执行替换
            new_text = re.sub(pattern, f'\\1{target_value}\\3', text)
            print(f"    替换结果: {new_text}")
            
            # 判断操作类型
            if match.group(2).strip():
                print(f"    操作类型: 替换 ('{match.group(2)}' → '{target_value}')")
            else:
                print(f"    操作类型: 填充 (空内容 → '{target_value}')")
        else:
            print(f"  ✗ 不匹配 (不会被处理)")
        
        print("-" * 80)

if __name__ == "__main__":
    test_regex_patterns()
